/**
 * My Account Login/Register Tabs
 * Giriş yap ve üye ol tab geçişleri
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        initMyAccountTabs();
    });

    function initMyAccountTabs() {
        const tabButtons = $('.dmrthema-tab-button');
        const tabPanels = $('.dmrthema-tab-panel');

        // Tab butonlarına tıklama olayı
        tabButtons.on('click', function(e) {
            e.preventDefault();
            
            const targetTab = $(this).data('tab');
            
            // Aktif tab butonunu güncelle
            tabButtons.removeClass('active');
            $(this).addClass('active');
            
            // Aktif panel'i güncelle
            tabPanels.removeClass('active');
            $('#' + targetTab + '-panel').addClass('active');
            
            // Smooth transition efekti
            const activePanel = $('#' + targetTab + '-panel');
            activePanel.css('opacity', '0');
            setTimeout(function() {
                activePanel.animate({opacity: 1}, 300);
            }, 50);
        });

        // Form validation ve submit handling
        $('.woocommerce-form-login').on('submit', function(e) {
            const username = $('#username').val().trim();
            const password = $('#password').val().trim();
            
            if (!username || !password) {
                e.preventDefault();
                showNotification('Lütfen tüm alanları doldurun.', 'error');
                return false;
            }
        });

        $('.woocommerce-form-register').on('submit', function(e) {
            const email = $('#reg_email').val().trim();
            const username = $('#reg_username').val().trim();
            const password = $('#reg_password').val().trim();
            
            if (!email) {
                e.preventDefault();
                showNotification('Lütfen e-posta adresinizi girin.', 'error');
                return false;
            }
            
            // E-posta format kontrolü
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                showNotification('Lütfen geçerli bir e-posta adresi girin.', 'error');
                return false;
            }
            
            // Username kontrolü (eğer gerekiyorsa)
            if ($('#reg_username').length && !username) {
                e.preventDefault();
                showNotification('Lütfen kullanıcı adınızı girin.', 'error');
                return false;
            }
            
            // Password kontrolü (eğer gerekiyorsa)
            if ($('#reg_password').length && !password) {
                e.preventDefault();
                showNotification('Lütfen şifrenizi girin.', 'error');
                return false;
            }
            
            if ($('#reg_password').length && password.length < 6) {
                e.preventDefault();
                showNotification('Şifreniz en az 6 karakter olmalıdır.', 'error');
                return false;
            }
        });

        // Input focus efektleri
        $('.dmrthema-form-group input').on('focus', function() {
            $(this).parent().addClass('focused');
        });

        $('.dmrthema-form-group input').on('blur', function() {
            $(this).parent().removeClass('focused');
        });

        // URL hash kontrolü - eğer register hash'i varsa register tab'ını aç
        if (window.location.hash === '#register') {
            $('[data-tab="register"]').click();
        }
    }

    // Bildirim gösterme fonksiyonu
    function showNotification(message, type) {
        // Mevcut bildirimleri kaldır
        $('.dmrthema-notification').remove();
        
        const notificationClass = type === 'error' ? 'dmrthema-notification-error' : 'dmrthema-notification-success';
        const notification = $('<div class="dmrthema-notification ' + notificationClass + '">' + message + '</div>');
        
        $('.dmrthema-login-wrapper').prepend(notification);
        
        // Animasyon ile göster
        notification.slideDown(300);
        
        // 5 saniye sonra otomatik kaldır
        setTimeout(function() {
            notification.slideUp(300, function() {
                $(this).remove();
            });
        }, 5000);
    }

})(jQuery);
