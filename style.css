/*
Theme Name: DmrThema
Author: Cline
Description: DmrThema için <PERSON> olarak geliştirilmiş bir tema.
Version: 1.0
*/

html {
    height: 100%;
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    overflow-x: hidden; /* <PERSON><PERSON>y taşmayı engelle */
}

main {
    flex: 1 0 auto;
}

.container {
    width: 75%;
    margin: 0 auto;
    max-width: 1200px; /* Maksimum genislik siniri */
}

/* Header Stilleri */
.site-header {
    border-bottom: 1px solid #e0e0e0;
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 0;
}

.logo a {
    text-decoration: none;
    color: #ff6000;
    font-size: 28px;
    font-weight: bold;
    display: block;
}

.site-logo {
    max-height: 60px;
    width: auto;
    display: block;
}

.search-form-container {
    flex-grow: 1;
    margin: 0 30px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
    min-height: 45px; /* Avatar yuksekligi ile uyumlu */
}

.search-form {
    display: flex;
    width: 100%;
}

.search-form label {
    flex-grow: 1;
}

.search-field {
    width: 100%;
    padding: 15px;
    border: 1px solid #ccc;
    border-right: none;
    border-radius: 5px 0 0 5px;
}

.search-submit {
    padding: 15px 20px;
    border: 1px solid #ff6000;
    background-color: #ff6000;
    color: white;
    cursor: pointer;
    border-radius: 0 5px 5px 0;
}

/* User Actions Genel Stilleri */
.user-actions {
    display: flex;
    align-items: center;
    padding-top: 5px;
}

/* Login Button Stilleri */
.login-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background-color: #ff6000;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    border: 2px solid #ff6000;
}

.login-button:hover {
    background-color: #e55500;
    border-color: #e55500;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 96, 0, 0.3);
}

.login-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.login-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
}

.login-main {
    font-size: 14px;
    font-weight: 500;
}

.login-sub {
    font-size: 11px;
    opacity: 0.9;
    font-weight: 400;
}

/* User Avatar ve Dropdown Stilleri */
.user-profile-dropdown {
    position: relative;
    display: inline-block;
}

.user-avatar-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.user-avatar-button:focus {
    outline: none;
}

/* Hover efekti icin avatar circle */
.user-avatar-circle {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #e0e0e0;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

/* Avatar hover efekti */
.user-profile-dropdown:hover .user-avatar-circle {
    border-color: #ff6000;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(255, 96, 0, 0.2);
}

.user-avatar-img {
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    object-fit: cover;
    display: block;
}

.user-dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    min-width: 240px;
    z-index: 1000;
    display: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.15s ease;
}

.user-dropdown-menu.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.user-info {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px 12px 0 0;
}

.user-name {
    display: block;
    font-weight: 600;
    color: #333;
    font-size: 14px;
    margin-bottom: 4px;
}

.user-email {
    display: block;
    color: #666;
    font-size: 12px;
    opacity: 0.8;
}

.user-menu-items {
    padding: 8px 0;
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 2px 8px;
}

.user-menu-item:hover {
    background-color: rgba(255, 96, 0, 0.08);
    border-left-color: #ff6000;
    color: #ff6000;
    transform: translateX(2px);
}

.user-menu-item .menu-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    opacity: 0.7;
    transition: all 0.2s ease;
}

.user-menu-item:hover .menu-icon {
    opacity: 1;
    transform: scale(1.1);
}

.user-menu-item.logout {
    border-top: 1px solid #f0f0f0;
    color: #dc3545;
    margin-top: 8px;
    padding-top: 16px;
}

.user-menu-item.logout:hover {
    background-color: rgba(220, 53, 69, 0.08);
    border-left-color: #dc3545;
    color: #dc3545;
}

/* WooCommerce Avatar Koruma - WooCommerce img kurallarini etkisiz hale getir */
.woocommerce .user-avatar-img,
.woocommerce-page .user-avatar-img {
    height: 100% !important;
    max-width: none !important;
    width: 100% !important;
}

.woocommerce .user-avatar-circle,
.woocommerce-page .user-avatar-circle {
    width: 45px !important;
    height: 45px !important;
}

/* Education Panel Stilleri */
.education-panel-button {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: #333;
    padding: 10px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    background-color: #ffffff;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.education-panel-button:hover {
    background-color: #fff;
    border-color: #ff6000;
    box-shadow: 0 4px 8px rgba(255, 96, 0, 0.15);
    transform: translateY(-1px);
}

.education-panel-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25px;
    height: 25px;
    background-color: #ff6000;
    border-radius: 5px;
    color: white;
    flex-shrink: 0;
    transition: transform 0.3s ease;
}

.education-panel-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.education-panel-main {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    line-height: 1.2;
}

.education-panel-sub {
    font-size: 11px;
    color: #666;
    line-height: 1.2;
}

/* Demir Stili Sepet Butonu */
.site-header-cart {
    line-height: 60px;
    margin-left: auto;
}

.demir-cart .cart-contents {
    padding: 0;
    color: #333;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

.demir-cart .cart-contents:hover {
    color: #ff6000;
}

.demir-cart .cart-contents .amount {
    font-size: 14px;
    font-weight: 650;
    color: #333;
}

.demir-cart .cart-contents:hover .amount {
    color: #ff6000;
}

/* Sepet ikonu stilleri */
.demir-cart-icon {
    position: relative;
    display: flex;
    align-items: center;
}

.demir-cart-icon svg {
    width: 40px;
    height: 40px;
    position: relative;
    top: -2px;
    stroke-width: 1.2px;
}

#cart-toggle > span.demir-cart-icon > svg > path {
    stroke-width: 1px;
}

/* Hover durumunda ürün sayısı turuncu */
#cart-toggle:hover .cart-item-count {
    color: #ff6000;
}

/* Sepet ikonunun her zaman gorunur olmasi */
.demir-cart-icon,
#cart-toggle > span.demir-cart-icon {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Sepet ürün sayısı */
.cart-item-count {
    position: absolute;
    top: 23px;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 15px;
    font-weight: bold;
    color: #333;
    z-index: 10;
    pointer-events: none;
}

/* Responsive ayarlar */
@media (max-width: 992px) {
    .site-header-cart {
        position: absolute;
        z-index: 2;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
    }

    .demir-cart .cart-contents .amount {
        display: none;
    }
}

.header-bottom {
    background-color: #F8F8F8;
    position: relative;
    z-index: 999; /* Mega menünün altında ama border görünür olsun */
}

.header-bottom .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
}

.main-navigation ul {
    display: flex;
    justify-content: flex-start; /* Elemanları sola hizalar */
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-navigation ul li {
}

.main-navigation ul li a {
    display: block;
    padding: 20px 15px;
    text-decoration: none;
    color: #333;
    font-weight: bold;
}

.main-navigation ul li a:hover {
    background-color: #e0e0e0;
}

/* Mega menü öğeleri için aşağı bakan ok simgesi */
.main-navigation ul li.has-mega-menu > a::after,
.main-navigation ul li.trend-mega-menu > a::after {
    content: "▼";
    font-size: 10px;
    margin-left: 8px;
    color: #666;
    transition: color 0.3s ease, transform 0.3s ease;
}

/* Mega menü aktif olduğunda ok simgesinin rengi */
.main-navigation ul li.mega-menu-active > a::after,
.main-navigation ul li.trend-mega-menu-active > a::after {
    color: #ff6000;
    transform: rotate(180deg);
}

/* Mega menü hover durumunda ok simgesinin rengi */
.main-navigation ul li.has-mega-menu > a:hover::after,
.main-navigation ul li.trend-mega-menu > a:hover::after {
    color: #ff6000;
}

/* Mega Menü Stilleri */
.main-navigation ul li.has-mega-menu {
    position: static; /* Konumlandırmayı header'a göre yapmak için */
}

.main-navigation ul .sub-menu {
    display: none;
    position: absolute;
    left: 0; /* Tam genişlik için */
    right: 0; /* Tam genişlik için */
    width: auto; /* Sol ve sağa göre otomatik genişlik */
    top: calc(100% + 1px); /* Header-bottom'un border-bottom'una tam yapışık */
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    padding: 20px 0;
    z-index: 1000; /* Yeterince yüksek z-index */
    margin-top: 0; /* Boşluk kaldırıldı */
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    pointer-events: none; /* Animasyon sirasinda tiklanabilirlik engelle */
}

/* JavaScript ile kontrol edilecek - CSS hover kaldiriliyor */
.main-navigation ul li.mega-menu-active > .sub-menu {
    display: block;
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto; /* Tiklanabilirlik aktif */
}

/* Genel mega menu container stilleri */
.sub-menu .sub-menu-container {
    width: 90%; /* Container'a uygun genişlik */
    max-width: 1200px; /* Ana container ile ayni maksimum genislik */
    margin: 0 auto;
    display: block; /* Varsayılan olarak block */
    padding: 20px; /* Genel padding */
    background: white;
    border-radius: 0;
    box-sizing: border-box;
}

/* Sadece bloksuz mega menüler için 4 sütunlu grid yapısı */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr; /* 4 sütunlu grid */
    gap: 20px; /* Sütunlar arası boşluk */
    min-height: 300px; /* Minimum yükseklik */
    align-items: start; /* İçeriği üstten hizala */
    padding: 20px; /* Grid için padding */
}

/* Bloklu mega menüler için eski stil korunur */
.sub-menu .sub-menu-container:has(.mega-menu-page-content) {
    display: block;
    padding: 0; /* Bloklu mega menüler için padding kaldır */
}

/* Grid sütun stilleri - sadece bloksuz mega menüler için */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 {
    grid-column: 1;
    border-right: 1px solid #e0e0e0;
    padding-right: 15px;
}

/* 2-4. sütunlar - Alt kategoriler için (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 {
    display: none; /* Başlangıçta gizli */
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 { grid-column: 2; }
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 { grid-column: 3; }
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 { grid-column: 4; }

/* Hover durumunda 2-4. sütunları göster (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2.active,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3.active,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4.active {
    display: block;
    opacity: 1;
}

/* Sütun başlıkları için stil (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 .column-title,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 .column-title,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 .column-title {
    font-weight: bold;
    color: #333;
    padding: 10px 0 10px 15px; /* Sol taraftan 15px boşluk eklendi */
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 10px;
    display: block;
}

/* Sütunlardaki ul elementleri için stil (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 ul,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 ul,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

/* Sütunlardaki li elementleri için stil (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 li,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 li,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 li {
    margin: 0;
    padding: 0;
}

/* Sütunlardaki link elementleri için stil (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 a {
    display: block;
    padding: 8px 15px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.2s ease;
}

/* Sütunlardaki link hover efekti (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a:hover,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a:hover,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a:hover,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 a:hover {
    background-color: #f5f5f5;
    color: #007cba;
}

/* Aktif menü öğeleri için stil (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a.active,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a.active,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a.active,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 a.active {
    background-color: #007cba;
    color: white;
}

/* Alt menüye sahip öğeler için ">" simgesi (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a.has-submenu::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a.has-submenu::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a.has-submenu::after {
    content: ">";
    float: right;
    font-weight: bold;
    color: #666;
    transition: color 0.2s ease;
    margin-right: 10px; /* Sağdan 10px boşluk ekleyerek sola taşı */
}

/* Alt menüye sahip linklerin yazı genişliğini azalt */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a.has-submenu,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a.has-submenu,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a.has-submenu {
    padding-right: 35px; /* Sağ taraftan daha fazla boşluk bırak */
}

/* Hover durumunda ">" simgesinin rengi */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a.has-submenu:hover::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a.has-submenu:hover::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a.has-submenu:hover::after {
    color: #007cba;
}

/* Aktif durumda ">" simgesinin rengi */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a.has-submenu.active::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a.has-submenu.active::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a.has-submenu.active::after {
    color: white;
}

/* Grid container için smooth transition (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) {
    transition: all 0.3s ease;
}

/* Sütunlar arası ayırıcı çizgiler (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 {
    border-right: 1px solid #f0f0f0;
    padding-right: 15px;
}

/* Son sütun için sağ border yok (sadece bloksuz mega menüler) */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 {
    border-right: none;
}

.sub-menu .sub-menu-container > li {
    width: 100%;
    padding-right: 0;
    box-sizing: border-box;
}

.sub-menu li {
    width: 100%;
}

/* Ana menü öğeleri (1. sütun) stilleri - sadece bloksuz mega menüler */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 li > a {
    display: block;
    padding: 12px 0 12px 20px !important; /* Sol taraftan 20px boşluk eklendi */
    font-weight: bold !important;
    color: #333 !important;
    text-decoration: none !important;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 li > a:hover,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 li > a.active {
    color: #ff6000 !important;
    background-color: #f8f8f8 !important;
}

/* 1. sütundaki has-submenu öğelerinin ">" simgesini koru */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 li > a.has-submenu:hover::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 li > a.has-submenu.active::after {
    content: ">" !important;
    float: right !important;
    font-weight: bold !important;
    color: #ff6000 !important;
    margin-right: 10px !important; /* Sağdan 10px boşluk ekleyerek sola taşı */
}

/* 2-4. sütunlar için genel stiller */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 {
    display: none; /* Varsayılan olarak gizli */
    padding: 0 15px 0 5px; /* Sol padding azaltıldı, sağ padding korundu */
}

.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2.active,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3.active,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4.active {
    display: block; /* Aktif olduğunda göster */
}

/* Alt kategori stilleri (2-4. sütunlar) - sadece bloksuz mega menüler */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 ul,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 ul,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 ul {
    list-style: none;
    padding: 0 0 0 15px; /* Sol taraftan 15px boşluk eklendi */
    margin: 0;
    display: flex;
    flex-direction: column; /* Dikey dizilim için */
}

/* 3. sütun için özel dikey dizilim */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 {
    display: flex;
    flex-direction: column; /* 3. sütundaki öğeleri dikey diz */
}

.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 > * {
    margin-bottom: 8px; /* Öğeler arası boşluk */
}

/* 4. sütun için özel dikey dizilim */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 {
    display: flex;
    flex-direction: column; /* 4. sütundaki öğeleri dikey diz */
}

.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 > * {
    margin-bottom: 8px; /* Öğeler arası boşluk */
}

.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 li,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 li,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 li {
    margin-bottom: 8px;
}

.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 li a,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 li a,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 li a {
    display: block;
    padding: 8px 0 8px 15px !important; /* Sol taraftan 15px boşluk eklendi */
    font-weight: normal !important;
    color: #666 !important;
    text-decoration: none !important;
    border-bottom: 1px solid #f5f5f5;
    transition: all 0.3s ease;
    cursor: pointer;
}

.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 li a:hover,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 li a:hover,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 li a:hover,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 li a.active,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 li a.active,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 li a.active {
    color: #ff6000 !important;
    background-color: #f8f8f8 !important;
}

/* 2-4. sütunlardaki has-submenu öğelerinin ">" simgesini koru */
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 li a.has-submenu:hover::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 li a.has-submenu:hover::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 li a.has-submenu:hover::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 li a.has-submenu.active::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 li a.has-submenu.active::after,
.sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 li a.has-submenu.active::after {
    content: ">" !important;
    float: right !important;
    font-weight: bold !important;
    color: #ff6000 !important;
    margin-right: 10px !important; /* Sağdan 10px boşluk ekleyerek sola taşı */
}

/* Eski stiller - geriye dönük uyumluluk için */
.sub-menu ul li a {
    padding: 8px 0 !important;
    font-weight: normal !important;
    color: #666 !important;
}

.sub-menu ul li a:hover {
    color: #ff6000 !important;
    background-color: transparent !important;
}

.sub-menu .sub-menu-container > li > a {
    font-weight: bold !important;
    color: #333 !important;
}

.sub-menu .sub-menu-container > li ul {
    list-style: none;
    padding-left: 0;
}

/* Mega Menu Sayfa Icerigi Stilleri - Sadece Konteyner */
.mega-menu-page-content {
    padding: 20px;
    background: white;
}

/* Liste ogelerinin dikey siralanmasi */
.mega-menu-page-content ul,
.mega-menu-page-content ol {
    display: block !important;
    list-style-position: inside !important;
}

.mega-menu-page-content li {
    display: list-item !important;
    width: 100% !important;
    float: none !important;
    clear: both !important;
}

/* Mega menu sayfa icerigindeki paragraph ve link stilleri */
.mega-menu-page-content p {
    margin: 0 !important;
    padding: 0 !important;
    line-height: inherit !important;
}

.mega-menu-page-content a {
    margin: 0 !important;
    padding: 0 !important;
    line-height: inherit !important;
    text-decoration: none !important;
    color: inherit !important;
    background-color: transparent !important;
    font-weight: inherit !important;
    font-size: inherit !important;
    font-family: inherit !important;
}

.mega-menu-page-content a:hover {
    color: #ff6000 !important;
    background-color: transparent !important;
}

.mega-menu-page-content a:active,
.mega-menu-page-content a:focus,
.mega-menu-page-content a:visited {
    background-color: transparent !important;
}

/* Mega menu icerigindeki tum elementlerin margin ve padding sifirlanmasi */
.mega-menu-page-content * {
    margin: 0 !important;
    padding: 0 !important;
}

/* Sadece container'in kendi padding'i korunur */
.mega-menu-page-content {
    padding: 20px !important;
}

/* Bloksuz mega menuler icin - sadece alt menuler varsa sayfa icerigi alani gizle */
.main-navigation ul li.has-mega-menu .sub-menu .sub-menu-container:empty {
    display: none;
}

/* Bloksuz mega menuler icin - sayfa icerigi yoksa container padding'i kaldir */
.main-navigation ul li.has-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) {
    padding: 0;
}

/* Mega menu container'da sadece alt menuler varsa ust padding kaldir */
.main-navigation ul li.has-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) > li:first-child {
    margin-top: 0;
}

/* Mega Menu Responsive Tasarım - sadece bloksuz mega menüler için */
@media (max-width: 1024px) {
    .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) {
        grid-template-columns: 1fr 1fr; /* Tablet için 2 sütun */
        gap: 15px;
        padding: 15px;
    }

    .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 {
        grid-column: 1;
        border-right: 1px solid #e0e0e0;
        padding-right: 10px;
    }

    .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 { grid-column: 2; }
    .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3,
    .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 {
        display: none !important; /* Tablet'te 3. ve 4. sütunları gizle */
    }
}

@media (max-width: 768px) {
    .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) {
        grid-template-columns: 1fr; /* Mobilde tek sütun */
        gap: 10px;
        padding: 10px;
    }

    .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 {
        border-right: none;
        padding-right: 0;
        margin-bottom: 15px;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 15px;
    }

    .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2,
    .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3,
    .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 {
        grid-column: 1;
        display: block !important; /* Mobilde tüm sütunları göster */
        opacity: 1 !important;
        margin-bottom: 15px;
    }
}

/* ========================================
   TREND MEGA MENU STILLERI
   ======================================== */

/* Trend Mega Menu Temel Stilleri */
.main-navigation ul li.trend-mega-menu {
    position: static; /* Konumlandırmayı header'a göre yapmak için */
}

.main-navigation ul li.trend-mega-menu .sub-menu {
    display: none;
    position: absolute;
    left: 0; /* Tam genişlik için */
    right: 0; /* Tam genişlik için */
    width: auto; /* Sol ve sağa göre otomatik genişlik */
    top: calc(100% + 1px); /* Header-bottom'un border-bottom'una tam yapışık */
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    padding: 20px 0;
    z-index: 1000; /* Yeterince yüksek z-index */
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    pointer-events: none; /* Tiklanabilirlik devre disi */
}

/* JavaScript ile kontrol edilecek - CSS hover kaldiriliyor */
.main-navigation ul li.trend-mega-menu-active > .sub-menu {
    display: block;
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto; /* Tiklanabilirlik aktif */
}

/* Genel trend mega menu container stilleri */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container {
    width: 90%; /* Container'a uygun genişlik */
    max-width: 1200px; /* Ana container ile ayni maksimum genislik */
    margin: 0 auto;
    display: block; /* Varsayılan olarak block */
    padding: 20px; /* Genel padding */
    background: white;
    border-radius: 0;
    box-sizing: border-box;
}

/* Sadece bloksuz trend mega menüler için 7 sütunlu grid yapısı */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) {
    display: grid;
    grid-template-columns: 0.9fr 0.1fr 0.5fr 0.5fr 0.5fr 0.5fr 0.5fr 0.5fr; /* 1. sütun biraz büyütüldü, boşluk azaltıldı */
    gap: 10px; /* Sütunlar arası boşluk */
    min-height: 300px; /* Minimum yükseklik */
    align-items: start; /* İçeriği üstten hizala */
    padding: 20px; /* Grid için padding */
}

/* Bloklu trend mega menüler için eski stil korunur */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:has(.mega-menu-page-content) {
    display: block;
    padding: 0; /* Bloklu mega menüler için padding kaldır */
}

/* Grid sütun stilleri - sadece bloksuz trend mega menüler için */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 {
    grid-column: 1;
    border-right: 1px solid #e0e0e0;
    padding-right: 15px;
    display: flex;
    flex-direction: column; /* 1. sütunu dikey sırala */
    background-color: #F8F8F8; /* Header bottom ile aynı gri arka plan */
}

/* 2-4. sütunlar - Alt kategoriler için (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 {
    display: none; /* Başlangıçta gizli, hover ile gösterilecek */
    opacity: 0;
    transition: opacity 0.3s ease;
}

.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 { grid-column: 3; } /* 2. sütun artık 3. pozisyonda */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 { grid-column: 4; }
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 { grid-column: 5; }
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-5 { grid-column: 6; }
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-6 { grid-column: 7; }
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-7 { grid-column: 8; }

/* Hover ile aktif olan sütunlar (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2.active,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3.active,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4.active,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-5.active,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-6.active,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-7.active {
    display: block;
    opacity: 1;
}

/* Sütun başlıkları için stil (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 .column-title,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 .column-title,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 .column-title {
    font-weight: bold;
    color: #333;
    padding: 10px 0 10px 15px; /* Sol taraftan 15px boşluk eklendi */
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 10px;
    display: block;
}

/* Sütunlardaki ul elementleri için stil (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 ul,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 ul,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

/* Sütunlardaki li elementleri için stil (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 li,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 li,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 li {
    margin: 0;
    padding: 0;
}

/* Sütunlardaki link elementleri için stil (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 a {
    display: block;
    padding: 8px 15px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.2s ease;
}

/* Sütunlardaki link hover efekti (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a:hover,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a:hover,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a:hover,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 a:hover {
    background-color: #f5f5f5;
    color: #007cba;
}

/* Aktif menü öğeleri için stil (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a.active,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a.active,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a.active,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 a.active {
    background-color: #007cba;
    color: white;
}

/* Alt menüye sahip öğeler için ">" simgesi (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a.has-submenu::after,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a.has-submenu::after,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a.has-submenu::after {
    content: ">";
    float: right;
    font-weight: bold;
    color: #666;
    transition: color 0.2s ease;
    margin-right: 10px; /* Sağdan 10px boşluk ekleyerek sola taşı */
}

/* Alt menüye sahip linklerin yazı genişliğini azalt */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a.has-submenu,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a.has-submenu,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a.has-submenu {
    padding-right: 35px; /* Sağ taraftan daha fazla boşluk bırak */
}

/* Hover durumunda ">" simgesinin rengi */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a.has-submenu:hover::after,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a.has-submenu:hover::after,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a.has-submenu:hover::after {
    color: #007cba;
}

/* Aktif durumda ">" simgesinin rengi */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 a.has-submenu.active::after,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 a.has-submenu.active::after,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 a.has-submenu.active::after {
    color: white;
}

/* Grid container için smooth transition (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) {
    transition: all 0.3s ease;
}

/* Sütunlar arası ayırıcı çizgiler (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3 {
    border-right: 1px solid #f0f0f0;
    padding-right: 15px;
}

/* Son sütun için sağ border yok (sadece bloksuz trend mega menüler) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 {
    border-right: none;
}

.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container > li {
    width: 100%;
    padding-right: 0;
    box-sizing: border-box;
}

.main-navigation ul li.trend-mega-menu .sub-menu li {
    width: 100%;
}

/* Ana menü öğeleri (1. sütun) stilleri - sadece bloksuz trend mega menüler */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 li > a {
    display: block;
    padding: 12px 0 12px 20px !important; /* Sol taraftan 20px boşluk eklendi */
    font-weight: bold !important;
    color: #333 !important;
    text-decoration: none !important;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 li > a:hover,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 li > a.active {
    color: #ff6000 !important;
    background-color: #f8f8f8 !important;
}

/* 1. sütun dikey sıralama için */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 ul {
    display: flex;
    flex-direction: column;
    list-style: none;
    margin: 0;
    padding: 0;
}

/* 2-7. sütunlar için genel stiller - Trendyol tarzı düz liste (artık 3-8. pozisyonlarda) */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-5,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-6,
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-7 {
    display: block; /* Her zaman görünür */
    padding: 0 8px 0 5px; /* Padding daha da azaltıldı - daha dar sütunlar için */
    max-height: none; /* Yükseklik sınırı kaldırıldı, JavaScript ile kontrol edilecek */
    overflow: visible; /* Taşan içeriği göster */
    border-right: none; /* Sağ border kaldırıldı */
    border-left: none; /* Sol border kaldırıldı */
}

/* Trend mega menu için özel düz liste yapısı */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item {
    display: block;
    margin-bottom: 1px; /* Genel margin azaltıldı */
}

/* Ana kategori başlıkları (kalın font) - 2-4. sütunlarda */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.main-category {
    font-weight: bold;
    color: #333; /* Siyah renk */
    padding: 12px 0 6px 0;
    border-bottom: none; /* Border kaldırıldı */
    margin-bottom: 6px;
    margin-top: 8px; /* Üstten boşluk */
    font-size: 15px;
}

/* İlk ana kategori için üst margin'i kaldır */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.main-category:first-child {
    margin-top: 0;
}

/* Alt kategori öğeleri (normal font) - 2-4. sütunlarda */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.sub-category {
    font-weight: normal;
    color: #333; /* Siyah renk */
    padding: 4px 0; /* Padding azaltıldı */
    font-size: 14px;
    margin-bottom: 1px; /* Margin azaltıldı */
}

/* Hover efektleri - sadece ana kategoriler için */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.main-category:hover {
    color: #ff6000;
    cursor: pointer;
}

/* Alt kategoriler için hover efekti - pointer cursor */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.sub-category {
    cursor: pointer; /* Tıklanabilir cursor */
}

.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.sub-category:hover {
    /* Alt başlıklar için hover efekti */
    color: #ff6000; /* Turuncu renk */
    background-color: transparent; /* Arka plan değişmez */
    text-decoration: underline; /* Alt çizgi */
}

/* Ana kategoriler için hover efekti - turuncu renk ve alt çizgi */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.main-category:hover {
    color: #ff6000; /* Turuncu renk */
    cursor: pointer;
    background-color: transparent; /* Arka plan değişmez */
    text-decoration: underline; /* Alt çizgi */
}

/* Trend mega menu için link stilleri */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item a {
    display: block;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 4px 0;
}

/* Ana kategori linkleri - 2-4. sütunlarda */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.main-category a {
    font-weight: bold;
    color: #333; /* Siyah renk */
    font-size: 15px;
    display: block;
    padding: 4px 0;
}

.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.main-category a:hover {
    color: #ff6000; /* Turuncu renk */
    background-color: transparent; /* Arka plan değişmez */
    text-decoration: underline; /* Alt çizgi */
    transition: all 0.2s ease; /* Yumuşak geçiş */
}

/* Alt kategori linkleri - 2-4. sütunlarda */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.sub-category a {
    font-weight: normal;
    color: #333; /* Siyah renk */
    font-size: 14px;
    display: block;
    padding: 4px 0;
    text-decoration: none; /* Alt çizgi kaldır */
}

/* Alt kategori linklerinde hover efekti - turuncu renk ve alt çizgi */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.sub-category a:hover {
    color: #ff6000; /* Turuncu renk */
    background-color: transparent; /* Arka plan değişmez */
    text-decoration: underline; /* Alt çizgi */
    transition: all 0.2s ease; /* Yumuşak geçiş */
}

/* Bloksuz trend mega menuler icin - sadece alt menuler varsa sayfa icerigi alani gizle */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:empty {
    display: none;
}

/* Bloksuz trend mega menuler icin - sayfa icerigi yoksa container padding'i kaldir */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) {
    padding: 0;
}

/* Trend mega menu container'da sadece alt menuler varsa ust padding kaldir */
.main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) > li:first-child {
    margin-top: 0;
}

/* Trend Mega Menu Responsive Tasarım - Trendyol tarzı düz liste için */
@media (max-width: 1024px) {
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) {
        grid-template-columns: 1fr 1fr; /* Tablet için 2 sütun */
        gap: 15px;
        padding: 15px;
    }

    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1,
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2 {
        display: block;
        max-height: none; /* Yükseklik sınırı kaldırıldı */
    }

    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3,
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4,
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-5,
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-6,
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-7 {
        display: none !important; /* Tablet'te 3-7. sütunları gizle */
    }
}

@media (max-width: 768px) {
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) {
        grid-template-columns: 1fr; /* Mobilde tek sütun */
        gap: 10px;
        padding: 10px;
    }

    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1,
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-2,
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-3,
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-4 {
        grid-column: 1;
        display: block !important; /* Mobilde tüm sütunları göster */
        max-height: none; /* Yükseklik sınırı kaldırıldı */
        margin-bottom: 15px;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 15px;
    }

    /* Mobilde ana kategori ve alt kategori font boyutlarını küçült */
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.main-category a {
        font-size: 14px;
    }

    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .trend-menu-item.sub-category a {
        font-size: 13px;
        padding-left: 10px; /* Mobilde daha az girinti */
    }

    /* Mobilde 1. sütundaki ana menü öğeleri için */
    .main-navigation ul li.trend-mega-menu .sub-menu .sub-menu-container:not(:has(.mega-menu-page-content)) .mega-menu-column-1 li > a {
        font-size: 14px !important;
        padding: 10px 0 10px 15px !important;
    }
}









/* Renkli Şerit */
.header-bottom::after {
    content: '';
    display: block;
    height: 4px;
    background: linear-gradient(to right,
        #ff6000 0%,
        #ff6000 15%,
        #41b6e6 25%,
        #41b6e6 35%,
        #8a3ffc 45%,
        #8a3ffc 55%,
        #4caf50 65%,
        #4caf50 75%,
        #5e35b1 85%,
        #5e35b1 100%);
}

/* Sidebar acikken body scroll'u engelle */
body.cart-sidebar-open {
    overflow: hidden !important;
}

/* HTML elementine de overflow hidden ekle */
html.cart-sidebar-open {
    overflow: hidden !important;
}

/* Sepet Sidebar Overlay */
.cart-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999998;
}

.cart-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Sepet Sidebar */
.cart-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    z-index: 1000000;
    transition: right 0.3s ease;
}

.cart-sidebar.active {
    right: 0;
}

.cart-sidebar-content {
    background-color: white;
    height: 100%;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden; /* Footer tasmasini onle */
}

.cart-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-sidebar-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.cart-sidebar-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: #666;
    transition: color 0.3s ease;
}

.cart-sidebar-close:hover {
    color: #333;
}

.cart-sidebar-body {
    flex: 1;
    overflow: hidden; /* Scroll widget_shopping_cart_content'te olacak */
    padding: 20px;
    padding-bottom: 0;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Flex child'in kuculebilmesi icin */
    max-height: calc(100vh - 140px); /* Header ve footer icin alan birak */
}

.cart-sidebar-footer {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    margin-top: auto;
}

.cart-footer-info {
    text-align: center;
}

.cart-footer-info p {
    margin: 0;
    color: #666;
}

.cart-footer-info small {
    font-size: 12px;
}

/* Mini Cart Stilleri */
.widget_shopping_cart_content {
    font-size: 14px;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Flex child'in kuculebilmesi icin */
    flex: 1; /* Parent'in tum alanini kullan */
    overflow: hidden; /* Child elementlerin tasmasini onle */
}

.widget_shopping_cart_content .woocommerce-mini-cart {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100vh - 320px); /* Header, footer ve padding icin alan */
    padding-bottom: 20px !important; /* Footer icin yeterli alan birak */

}



.widget_shopping_cart_content .woocommerce-mini-cart-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 0 !important;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
    z-index: 1;
}

.widget_shopping_cart_content .woocommerce-mini-cart-item:last-child {
    border-bottom: none;
}

/* Urun resimleri - tutarli boyut */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item img {
    width: 50px !important;
    height: 50px !important;
    object-fit: cover !important;
    border-radius: 5px !important;
    margin-right: 12px !important;
    flex-shrink: 0 !important;
}

/* Remove butonu - tutarli pozisyon */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .remove {
    position: absolute !important;
    top: 10px !important;
    right: 0 !important;
    color: #ff4444 !important;
    text-decoration: none !important;
    font-weight: bold !important;
    font-size: 16px !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    background: rgba(255, 68, 68, 0.1) !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .remove:hover {
    color: #cc0000 !important;
    background: rgba(204, 0, 0, 0.1) !important;
}

/* Urun bilgileri - tutarli gorunum */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-details {
    flex: 1 !important;
    padding-right: 25px !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name {
    display: block !important;
    text-decoration: none !important;
    color: #333 !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
    line-height: 1.3 !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name:hover {
    color: #ff6000 !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-info {
    margin-top: 5px !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .quantity {
    color: #666 !important;
    font-size: 12px !important;
    display: block !important;
}

/* Cart Footer Stilleri - Normal flow */
.widget_shopping_cart_content .cart-footer {
    margin-top: auto;
    padding: 20px 0;
    background-color: #fff;
    border-top: 2px solid #e9ecef;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    flex-shrink: 0; /* Footer'in kuculmamasini sagla */
}

/* Toplam fiyat */
.widget_shopping_cart_content .woocommerce-mini-cart__total {
    padding: 0 0 15px 0;
    margin: 0;
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 15px;
}

.widget_shopping_cart_content .woocommerce-mini-cart__total .amount {
    color: #ff6000;
    font-size: 18px;
}

/* Butonlar */
.widget_shopping_cart_content .woocommerce-mini-cart__buttons {
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button {
    display: block;
    width: 90%;
    text-align: center;
    padding: 12px 15px;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
    font-weight: 500;
    border: none;
    cursor: pointer;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.wc-forward {
    background-color: #fff;
    color: #333;
    border: 1px solid #dee2e6;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.wc-forward:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.checkout {
    background-color: #ff6000;
    color: white;
    border: 1px solid #ff6000;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.checkout:hover {
    background-color: #e55a00;
    border-color: #e55a00;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 96, 0, 0.3);
}

/* Bos sepet mesaji */
.widget_shopping_cart_content .woocommerce-mini-cart__empty-message {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 0;
    border: 1px solid #e9ecef;
}

/* SIDEBAR TUTARLILIGI - TUM SAYFALARDA AYNI GORUNUM */
/* En yuksek oncelik ile tum CSS cakismalarini onle */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.home .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.shop .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.single-product .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.archive .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-shop .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-page .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
    padding: 12px 0 !important;
    margin: 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
    display: flex !important;
    align-items: flex-start !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Mini cart listesi icin tutarli stil */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart {
    padding: 0 !important;
    margin: 0 !important;
    list-style: none !important;
}

/* Son elementin border'ini kaldir */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item:last-child {
    border-bottom: none !important;
}

/* Responsive */
@media (max-width: 768px) {
    .cart-sidebar {
        width: 100vw;
        right: -100vw;
    }

    .cart-sidebar-content {
        width: 100%;
    }

    .cart-sidebar-body {
        padding: 15px;
        padding-bottom: 0;
        max-height: calc(100vh - 120px); /* Mobilde daha az header/footer */
    }

    .cart-sidebar-footer {
        padding: 15px;
    }

    .widget_shopping_cart_content .cart-footer {
        padding: 15px 0;
    }

    .widget_shopping_cart_content .woocommerce-mini-cart {
        max-height: calc(100vh - 280px) !important; /* Mobil icin ayarla */
    }

    html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item img {
        width: 40px !important;
        height: 40px !important;
        margin-right: 10px !important;
    }

    html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
        padding: 12px 0 !important;
    }

    /* User Actions Mobil Stilleri */
    .login-button {
        padding: 8px 12px;
        font-size: 13px;
        gap: 6px;
    }

    .login-icon {
        width: 14px;
        height: 14px;
    }

    .login-main {
        font-size: 13px;
    }

    .login-sub {
        font-size: 10px;
    }

    /* User Avatar Mobil Stilleri */
    .user-avatar-circle {
        width: 35px !important;
        height: 35px !important;
    }

    /* WooCommerce Avatar Koruma - Mobil */
    .woocommerce .user-avatar-circle,
    .woocommerce-page .user-avatar-circle {
        width: 35px !important;
        height: 35px !important;
    }

    .user-dropdown-menu {
        right: -10px;
        min-width: 220px;
    }

    .user-info {
        padding: 14px;
    }

    .user-name {
        font-size: 13px;
    }

    .user-email {
        font-size: 11px;
    }

    .user-menu-item {
        padding: 10px 14px;
        font-size: 13px;
        margin: 2px 6px;
    }

    .user-menu-item .menu-icon {
        width: 14px;
        height: 14px;
    }

    .user-menu-item {
        padding: 10px 14px;
        font-size: 13px;
        gap: 8px;
    }

    .user-menu-item .menu-icon {
        width: 14px;
        height: 14px;
    }
}

/* Footer Stilleri */
.site-footer {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 40px 0;
    margin-top: 40px;
}

.footer-widgets {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
}

.widget-area {
    width: 30%;
}

.widget-area h3 {
    color: #fff;
    border-bottom: 2px solid #ff6000;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.widget-area ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.widget-area ul li a {
    color: #ecf0f1;
    text-decoration: none;
    line-height: 2;
    transition: color 0.3s;
}

.widget-area ul li a:hover {
    color: #ff6000;
}

.footer-bottom {
    text-align: center;
    border-top: 1px solid #34495e;
    padding-top: 20px;
}

.footer-bottom p {
    margin: 0;
    color: #bdc3c7;
}

/* Slider Stilleri */
.main-slider {
    width: 100%;
    max-width: 100%;
    height: 500px;
    margin-bottom: 40px;
    margin-top: 0;
    overflow: hidden; /* Taşan içeriği gizle */
    position: relative;
}

/* Swiper wrapper'ı temizle */
.main-slider .swiper-wrapper {
    margin: 0 !important;
    padding: 0 !important;
}

.swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;

    /* Center slide text vertically */
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
}

.swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    max-width: 100%; /* Görüntünün container'dan taşmasını engelle */
}

/* Slider içeriğindeki gereksiz boşlukları kaldır */
.swiper-slide p {
    margin: 0 !important;
    padding: 0 !important;
}

.swiper-slide br {
    display: none !important;
}

/* Video container içindeki boşlukları kaldır */
.swiper-slide .video-container p {
    margin: 0 !important;
    padding: 0 !important;
}

.swiper-slide .video-container br {
    display: none !important;
}

.video-container {
    position: relative;
    width: 100%;
    height: 100%;
    margin: 0 !important;
    padding: 0 !important;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0 !important;
    padding: 0 !important;
}

/* WordPress editöründen gelen gereksiz elementleri kaldır */
.video-container > p:empty,
.video-container > br,
.video-container > div:empty {
    display: none !important;
}

/* Slider içindeki tüm WordPress editör boşluklarını kaldır */
.swiper-slide > p:empty,
.swiper-slide > br:first-child,
.swiper-slide > br:last-child,
.swiper-slide > div:empty {
    display: none !important;
}

/* WordPress wpautop fonksiyonundan gelen gereksiz p etiketlerini kaldır */
.swiper-slide p:only-child:empty {
    display: none !important;
}

/* İlk ve son br etiketlerini kaldır */
.swiper-slide > br:first-of-type,
.swiper-slide > br:last-of-type {
    display: none !important;
}

/* WooCommerce Temel Stiller */
/* WooCommerce sayfalarinda header ile tutarli hizalama */
.woocommerce .site-main,
.woocommerce-page .site-main {
    margin: 0 auto;
    /* width: 75%; */ /* Header container ile ayni genislik */
    max-width: 1200px; /* Container ile ayni maksimum genislik */
}

/* WooCommerce content wrapper'i */
.woocommerce .content-area,
.woocommerce-page .content-area {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Urun sayfasi icin ozel hizalama */
.single-product .woocommerce {
    /* width: 75%; */
    max-width: 1200px;
    margin: 0 auto;
}

/* Mobil cihazlarda urun sayfasi */
@media (max-width: 768px) {
    .single-product .woocommerce {
        /* width: 95%; */
        padding: 0 10px;
    }
}

/* Checkout Sayfasi Ozel Stiller */
.checkout-page .site-header {
    border-bottom: 1px solid #e0e0e0;
    background: #fff;
}

.checkout-page .site-header .header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
}

.checkout-page .ssl-security {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.checkout-page .ssl-icon {
    width: 50px;
    height: 32px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 11px;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.checkout-page .ssl-text {
    font-weight: 600;
    color: #28a745;
    letter-spacing: 0.3px;
}



/* Checkout sayfasinda footer'i gizle */
.checkout-page .site-footer {
    display: none !important;
}

/* Checkout sayfasinda sayfa basligini gizle */
.checkout-page #post-14 > header,
.checkout-page .woocommerce .entry-header,
.checkout-page .entry-header {
    display: none !important;
}

/* Post 14 icindeki div yapilandirmasi icin margin */
#post-14 > div > div > div {
    margin-top: 35px;
}

/* Post 14 icindeki ul listesi icin stil */
#post-14 > div > div > div > ul {
    margin-left: 0;
    padding: 15px;
    border: 1px solid #e2e2e2;
}

/* Banka detaylari section icindeki ul listesi icin stil */
#post-14 > div > div > div > section.woocommerce-bacs-bank-details > ul {
    margin-left: 0;
    padding: 15px;
    border: 1px solid #e2e2e2;
}

/* Tesekkur mesaji icin stil */
#post-14 > div > div > div > p {
    font-weight: bold;
    margin-bottom: 20px;
}

/* ===== MY ACCOUNT LOGIN PAGE STYLES ===== */
body.myaccount-login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* Header ve sayfa basligini gizle */
body.myaccount-login-page .site-header {
    display: none !important;
}

body.myaccount-login-page .entry-header {
    display: none !important;
}

body.myaccount-login-page .entry-title {
    display: none !important;
}

body.myaccount-login-page .container {
    padding: 0 !important;
    margin: 0 !important;
    max-width: none !important;
}

body.myaccount-login-page #primary {
    margin: 0 !important;
    padding: 0 !important;
}

body.myaccount-login-page .site-main {
    margin: 0 !important;
    padding: 0 !important;
}

/* WooCommerce my-account sayfasindaki gereksiz elementleri gizle */
body.myaccount-login-page .woocommerce-MyAccount-navigation {
    display: none !important;
}

body.myaccount-login-page .woocommerce .col2-set {
    display: none !important;
}

body.myaccount-login-page article {
    margin: 0 !important;
    padding: 0 !important;
}

body.myaccount-login-page .entry-content {
    margin: 0 !important;
    padding: 0 !important;
}

/* Sadece login formunu goster */
body.myaccount-login-page .woocommerce {
    margin: 0 !important;
    padding: 0 !important;
}

/* Varsayilan WooCommerce login formunu gizle */
body.myaccount-login-page .u-columns.col2-set {
    display: none !important;
}

body.myaccount-login-page .u-column1.col-1 {
    display: none !important;
}

body.myaccount-login-page .u-column2.col-2 {
    display: none !important;
}

/* Sadece bizim custom formumuz gorunsun */
body.myaccount-login-page .dmrthema-login-container {
    display: flex !important;
}

.dmrthema-login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.dmrthema-login-wrapper {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 450px;
    position: relative;
    overflow: hidden;
}

.dmrthema-login-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6000, #e55500);
}

.dmrthema-login-logo {
    text-align: center;
    margin-bottom: 30px;
}

.dmrthema-login-logo img {
    max-height: 60px;
    width: auto;
}

.dmrthema-login-tabs {
    display: flex;
    margin-bottom: 30px;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 4px;
}

.dmrthema-tab-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    background: transparent;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dmrthema-tab-button.active {
    background: white;
    color: #ff6000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dmrthema-tab-button svg {
    width: 18px;
    height: 18px;
}

.dmrthema-tab-content {
    position: relative;
}

.dmrthema-tab-panel {
    display: none;
}

.dmrthema-tab-panel.active {
    display: block;
}

.dmrthema-form-group {
    margin-bottom: 20px;
}

.dmrthema-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.dmrthema-form-group input[type="text"],
.dmrthema-form-group input[type="email"],
.dmrthema-form-group input[type="password"] {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.dmrthema-form-group input:focus {
    outline: none;
    border-color: #ff6000;
    box-shadow: 0 0 0 3px rgba(255, 96, 0, 0.1);
}

.dmrthema-form-remember {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
}

.dmrthema-form-remember label {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    cursor: pointer;
    font-size: 13px;
}

.dmrthema-form-remember input[type="checkbox"] {
    margin-right: 8px;
    width: auto;
}

.dmrthema-submit-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #ff6000, #e55500);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: none;
}

.dmrthema-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 96, 0, 0.3);
}

.dmrthema-form-links {
    text-align: center;
    margin-top: 20px;
}

.dmrthema-form-links a {
    color: #ff6000;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
}

.dmrthema-form-links a:hover {
    text-decoration: underline;
}

.dmrthema-form-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.dmrthema-form-info p {
    margin: 0;
    font-size: 13px;
    color: #6c757d;
}

.required {
    color: #dc3545;
}

/* Notification Styles */
.dmrthema-notification {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: 500;
    display: none;
}

.dmrthema-notification-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.dmrthema-notification-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* WooCommerce Error Messages Override */
body.myaccount-login-page .woocommerce-error,
body.myaccount-login-page .woocommerce-message,
body.myaccount-login-page .woocommerce-info {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    font-size: 14px;
}

body.myaccount-login-page .woocommerce-message {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

body.myaccount-login-page .woocommerce-info {
    background: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dmrthema-login-wrapper {
        padding: 30px 20px;
        margin: 10px;
        border-radius: 15px;
    }

    .dmrthema-tab-button {
        padding: 10px 12px;
        font-size: 13px;
    }

    .dmrthema-tab-button svg {
        width: 16px;
        height: 16px;
    }
}





/* Responsive tasarim */
@media (max-width: 768px) {
    .checkout-page .ssl-security {
        font-size: 12px;
        gap: 8px;
    }

    .checkout-page .ssl-icon {
        width: 40px;
        height: 28px;
        font-size: 10px;
    }
}

/* WooCommerce Ürün Grid - Responsive Grid */
.woocommerce ul.products {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 20px !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Desktop icin 4 sutun */
@media (min-width: 1024px) {
    .woocommerce ul.products {
        grid-template-columns: repeat(4, 1fr) !important;
    }
}

/* Tablet icin 3 sutun */
@media (max-width: 1023px) and (min-width: 768px) {
    .woocommerce ul.products {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

/* Mobil icin 2 sutun */
@media (max-width: 767px) {
    .woocommerce ul.products {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
    }
}

.woocommerce ul.products li.product {
    width: auto !important;
    margin: 0 !important;
    float: none !important;
    clear: none !important;
    display: block !important;
}

/* WooCommerce pseudo-elementleri temizle */
.woocommerce .products ul::after,
.woocommerce .products ul::before,
.woocommerce ul.products::after,
.woocommerce ul.products::before {
    content: none !important;
    display: none !important;
}



/* Alttaki sonuc sayisini ve sıralama butonunu gizle - spesifik elementler */
#main > div.columns-3 > div > p,
#main > div.columns-3 > div > form {
    display: none !important;
}

/* Ürün Detay Sayfası */
.product-details-wrapper {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.product .images {
    flex: 1;
}

.product .summary {
    flex: 1;
}

@media (max-width: 768px) {
    .product-details-wrapper {
        flex-direction: column;
        gap: 20px;
    }
}

/* WooCommerce Sepetim Butonlarini Gizle */
/* Sepete ekleme sonrasi cikan "Sepetim" butonlarini gizler */
.woocommerce ul.products li.product .added_to_cart,
.woocommerce-page ul.products li.product .added_to_cart,
.added_to_cart.wc-forward {
    display: none !important;
}

/* Header Responsive Stilleri */
@media (max-width: 768px) {
    .container {
        width: 95%; /* Mobilde daha genis kullanim */
        padding: 0 10px;
    }

    /* Mobilde mega menü ok simgesi boyutu */
    .main-navigation ul li.has-mega-menu > a::after,
    .main-navigation ul li.trend-mega-menu > a::after {
        font-size: 8px;
        margin-left: 6px;
    }

    .header-top .container {
        flex-direction: column;
        gap: 15px;
        padding: 20px 10px;
    }

    .search-form-container {
        order: 3;
        margin: 0;
        width: 100%;
    }

    .header-right {
        order: 2;
        gap: 15px;
        width: 100%;
        justify-content: space-between;
    }

    .logo {
        order: 1;
    }

    .education-panel {
        display: none; /* Mobilde egitim paneli gizle */
    }

    /* WooCommerce mobil hizalama */
    .woocommerce .site-main,
    .woocommerce-page .site-main {
        width: 95%;
        padding: 0 10px;
    }

    /* Slider mobil ayarları */
    .main-slider {
        height: 300px; /* Mobilde daha kısa yükseklik */
        margin-bottom: 20px;
        margin-top: 0;
    }

    /* Mobilde slider içeriğini temizle */
    .swiper-slide p,
    .swiper-slide br {
        display: none !important;
    }
}

@media (max-width: 480px) {
    .header-right {
        gap: 10px;
    }

    .login-button {
        padding: 6px 10px;
        font-size: 12px;
        gap: 4px;
    }

    .login-icon {
        width: 12px;
        height: 12px;
    }

    .login-sub {
        display: none; /* Mobilde "veya uye ol" metnini gizle */
    }

    .user-avatar-circle {
        width: 32px !important;
        height: 32px !important;
    }

    /* WooCommerce Avatar Koruma - Cok Kucuk Ekranlar */
    .woocommerce .user-avatar-circle,
    .woocommerce-page .user-avatar-circle {
        width: 32px !important;
        height: 32px !important;
    }
}

/* Main Header H1 Stilleri */
#main > header > h1 {
    margin-bottom: 10px;
    margin-top: 35px;
}

/* Post-15 navigasyon sol yeşil boşluğunu kaldır */
#post-15 > div > div > nav > ul {
    border-left: none !important;
    margin-left: 0 !important;
    padding-left: 0 !important;
}

/* WooCommerce Degerlendirme Bolumu - Etiketleri Gizle */
#comment-form-rating-label,
#commentform > p.comment-form-comment > label {
    display: none !important;
}

/* WooCommerce Reviews Form Paragraph Margin */
.woocommerce #review_form #respond p {
    margin: 10px 0 10px;
}

/* WooCommerce Reviews Form Textarea Padding */
.woocommerce #review_form #respond textarea {
    padding: 10px;
}

/* Ana Sayfa Urun Bolumleri */
.homepage-section {
    margin-bottom: 100px;
}

.homepage-section .section-header {
    text-align: center;
    margin-bottom: 40px;
}

.homepage-section .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
    position: relative;
}

.homepage-section .section-title:after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #ff6000, #ff8533);
    margin: 15px auto;
    border-radius: 2px;
}

.homepage-section .section-description {
    font-size: 1.1rem;
    color: #666;
    margin: 0;
    font-weight: 400;
}

/* Urun Grid Duzenlemesi - WooCommerce Uyumlu */
.homepage-section .products-grid {
    margin-bottom: 40px;
}

/* Ana sayfa urun grid'i - Tam 4'lu grid */
.homepage-section ul.products {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    row-gap: 40px !important; /* satir arasi boşluk */
    column-gap: 20px !important; /* sutun arasi boşluk */
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

.homepage-section ul.products li.product {
    width: auto !important;
    padding: 0 !important;
    margin: 0 !important;
    list-style: none !important;
    position: relative;
    transition: all 0.3s ease;
    box-sizing: border-box !important;
}

/* Responsive grid */
@media (max-width: 1200px) {
    .homepage-section ul.products {
        grid-template-columns: repeat(3, 1fr) !important;
        row-gap: 35px !important;
        column-gap: 18px !important;
    }
}

@media (max-width: 768px) {
    .homepage-section ul.products {
        grid-template-columns: repeat(2, 1fr) !important;
        row-gap: 30px !important;
        column-gap: 15px !important;
    }
}

@media (max-width: 480px) {
    .homepage-section ul.products {
        grid-template-columns: 1fr !important;
        row-gap: 25px !important;
        column-gap: 0 !important;
    }
}

/* WooCommerce varsayilan margin'larini sifirla */
.homepage-section ul.products li.product,
.homepage-section .woocommerce ul.products li.product,
.homepage-section .woocommerce-page ul.products li.product {
    margin: 0 !important;
    margin-bottom: 0 !important;
    margin-top: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Ana sayfa urun kartlari - Gorusel ile ayni */
.homepage-section ul.products li.product {
    background: #fff !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    padding: 0 0 60px 0 !important;
    margin: 0 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: all 0.3s ease;
    box-sizing: border-box !important;
    position: relative !important;
}

.homepage-section ul.products li.product:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    border-color: #ccc !important;
}

/* Urun resmi wrapper */
.homepage-section ul.products li.product .woocommerce-loop-product__link {
    display: block !important;
    margin: 0 !important;
    border-radius: 8px 8px 0 0 !important;
    overflow: hidden;
    position: relative;
}

.homepage-section ul.products li.product img {
    width: 100% !important;
    height: 180px !important;
    object-fit: cover !important;
    border-radius: 8px 8px 0 0 !important;
    transition: transform 0.3s ease !important;
    display: block;
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

.homepage-section ul.products li.product:hover img {
    transform: scale(1.02) !important;
}

/* Urun bilgileri icin padding */
.homepage-section ul.products li.product .woocommerce-loop-product__title,
.homepage-section ul.products li.product .price,
.homepage-section ul.products li.product .star-rating {
    margin-left: 15px !important;
    margin-right: 15px !important;
}

.homepage-section ul.products li.product .woocommerce-loop-product__title {
    margin-top: 15px !important;
}

/* Indirim etiketi */
.homepage-section ul.products li.product .onsale {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    background: #e74c3c !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 3px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    z-index: 2 !important;
    text-transform: uppercase !important;
}

/* Urun icerik alani */
.homepage-section ul.products li.product > * {
    flex-shrink: 0;
}

.homepage-section ul.products li.product .woocommerce-loop-product__link:last-child {
    margin-top: auto;
}

/* Urun basligi */
.homepage-section ul.products li.product .woocommerce-loop-product__title {
    font-size: 16px !important;
    font-weight: 500 !important;
    margin: 0 0 10px 0;
    line-height: 1.3 !important;
    display: flex;
    align-items: flex-start;
}

.homepage-section ul.products li.product .woocommerce-loop-product__title a {
    color: #333 !important;
    text-decoration: none !important;
    transition: color 0.3s ease !important;
}

.homepage-section ul.products li.product .woocommerce-loop-product__title a:hover {
    color: #0073aa !important;
}

/* Yildiz puanlama */
.homepage-section ul.products li.product .star-rating {
    margin-bottom: 8px !important;
    font-size: 13px !important;
    color: #ffc107 !important;
}

.homepage-section ul.products li.product .star-rating span {
    color: #ffc107 !important;
}

/* Fiyat */
.homepage-section ul.products li.product .price {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #ff6000 !important;
    margin-bottom: 15px !important;
    display: block !important;
    min-height: 25px;
}

.homepage-section ul.products li.product .price del {
    color: #999 !important;
    font-weight: normal !important;
    margin-right: 5px;
}

/* Sepete ekle butonu - Hover'da gorunur */
.homepage-section ul.products li.product .button,
.homepage-section ul.products li.product .add_to_cart_button {
    width: 85% !important;
    background: #ff6000 !important;
    color: white !important;
    border: none !important;
    padding: 10px 15px !important;
    border-radius: 4px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
    display: block !important;
    cursor: pointer !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateX(-50%) translateY(10px) !important;
    margin: 0 auto 15px auto !important;
    font-size: 14px !important;
    position: absolute !important;
    bottom: 0px !important;
    left: 50% !important;
    z-index: 20 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* Hover'da butonu goster */
.homepage-section ul.products li.product:hover .button,
.homepage-section ul.products li.product:hover .add_to_cart_button {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(-50%) translateY(0) !important;
}

.homepage-section ul.products li.product .button:hover,
.homepage-section ul.products li.product .add_to_cart_button:hover {
    background: #e55500 !important;
    color: white !important;
    transform: translateX(-50%) translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

/* Istek listesi butonu - Hover'da gorunur */
.homepage-section ul.products li.product .yith-wcwl-add-to-wishlist,
.homepage-section ul.products li.product .wishlist-button,
.homepage-section ul.products li.product .add_to_wishlist,
.homepage-section ul.products li.product > button {
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    z-index: 25 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s ease !important;
}

/* Hover'da istek listesi butonunu goster */
.homepage-section ul.products li.product:hover .yith-wcwl-add-to-wishlist,
.homepage-section ul.products li.product:hover .wishlist-button,
.homepage-section ul.products li.product:hover .add_to_wishlist,
.homepage-section ul.products li.product:hover > button {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

/* Bolum Footer */
.homepage-section .section-footer {
    text-align: center;
}

.homepage-section .view-all-btn {
    display: inline-block;
    padding: 15px 30px;
    background: transparent;
    color: #ff6000;
    border: 2px solid #ff6000;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.homepage-section .view-all-btn:hover {
    background: #ff6000;
    color: white;
    transform: translateY(-2px);
}

/* Urun Bulunamadi Mesaji */
.homepage-section .no-products {
    text-align: center;
    color: #666;
    padding: 60px 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    margin: 0;
    border: 2px dashed #dee2e6;
}

.homepage-section .no-products h3 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1.5rem;
    font-weight: 600;
}

.homepage-section .no-products p {
    color: #6c757d;
    margin: 0;
    font-size: 1rem;
}

/* Responsive Tasarim */
@media (max-width: 768px) {
    .homepage-section {
        margin-bottom: 30px;
    }

    .homepage-section .section-title {
        font-size: 2rem;
    }

    .homepage-section ul.products li.product {
        padding: 20px !important;
    }

    .homepage-section ul.products li.product img {
        height: 180px !important;
    }
}

@media (max-width: 480px) {
    .homepage-section .section-title {
        font-size: 1.8rem;
    }

    .homepage-section ul.products li.product {
        padding: 15px !important;
    }

    .homepage-section ul.products li.product img {
        height: 160px !important;
    }

    .homepage-section ul.products li.product .woocommerce-loop-product__title {
        min-height: auto !important;
        margin-bottom: 10px !important;
    }
}
